"""
Lang<PERSON>hain tool for generating JSON templates following expedition format specification.
"""

import json
import logging
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Type

from langchain.tools import BaseTool
from pydantic import BaseModel, Field

from ..config.langchain_config import JSON_TEMPLATE_STRUCTURE, VALIDATION_RULES

logger = logging.getLogger(__name__)


class TemplateGeneratorInput(BaseModel):
    """Input schema for template generator tool."""

    extracted_data: str = Field(description="Extracted expedition data in JSON format")
    operation_type: str = Field(
        default="combined", description="Operation type: am_only, pm_only, or combined"
    )
    location: str = Field(
        default="Unknown_Location", description="Operation location name"
    )
    output_path: str = Field(
        default="./consolidated_outputs", description="Path where to save the generated JSON template"
    )


class TemplateGeneratorTool(BaseTool):
    """Tool for generating JSON templates that follow the expedition format specification."""

    name: str = "template_generator"
    description: str = """
    Generate JSON templates following the expedition format specification from extracted data.
    Creates separate JSON files for different operation types (AM-only, PM-only, combined).
    Validates data against format requirements and populates all required fields.
    """
    args_schema: Type[BaseModel] = TemplateGeneratorInput

    # Define validation rules as class attributes to avoid Pydantic field issues
    _validation_rules: Dict[str, Any] = {
        "required_fields": ["date", "location", "activities"],
        "time_format": r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        "date_format": r"^\d{4}-\d{2}-\d{2}$",
    }
    _template_structure: Dict[str, Any] = {}

    def __init__(self):
        super().__init__()
        # Initialize with config values or fallback
        try:
            self._validation_rules = VALIDATION_RULES
            self._template_structure = JSON_TEMPLATE_STRUCTURE
        except Exception:
            pass  # Use class defaults

    def _run(
        self,
        extracted_data: str,
        operation_type: str = "combined",
        location: str = "Unknown_Location",
        output_path: str = "./output",
    ) -> str:
        """Generate JSON template from extracted data."""
        try:
            # Parse extracted data
            try:
                data = json.loads(extracted_data)
            except json.JSONDecodeError as e:
                return f"Error: Invalid JSON in extracted_data: {e!s}"

            # Validate operation type
            valid_types = ["am_only", "pm_only", "combined"]
            if operation_type not in valid_types:
                return f"Error: Invalid operation_type. Must be one of: {valid_types}"

            # Generate template
            template = self._create_expedition_template(data, operation_type, location)

            # Validate template
            validation_result = self._validate_template(template)
            if validation_result != "valid":
                return f"Error: Template validation failed: {validation_result}"

            # Save template
            output_file = self._save_template(
                template, output_path, operation_type, location
            )

            logger.info(f"Generated template: {output_file}")
            return f"Successfully generated JSON template: {output_file}"

        except Exception as e:
            error_msg = f"Error generating template: {e!s}"
            logger.error(error_msg)
            return error_msg

    def _create_expedition_template(
        self, data: Dict[str, Any], operation_type: str, location: str
    ) -> Dict[str, Any]:
        """Create expedition template from extracted data using the expected flat format."""
        return self._create_simple_template_from_data(data, operation_type, location)

    def _create_simple_template_from_data(
        self, data: Dict[str, Any], operation_type: str, location: str
    ) -> Dict[str, Any]:
        """Create simple flat template format from extracted data with improved validation."""
        from datetime import datetime
        import re

        # Extract date from data or use current date
        date_str = data.get("date", datetime.now().strftime("%Y-%m-%d"))

        # Parse date to get weekday
        try:
            if date_str and date_str != "Not specified":
                # Handle various date formats
                if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                else:
                    # Try to parse other formats
                    from dateutil import parser
                    try:
                        date_obj = parser.parse(date_str, fuzzy=True)
                        date_str = date_obj.strftime("%Y-%m-%d")
                    except:
                        # Use current date if parsing fails
                        date_obj = datetime.now()
                        date_str = date_obj.strftime("%Y-%m-%d")
            else:
                date_obj = datetime.now()
                date_str = date_obj.strftime("%Y-%m-%d")
        except:
            date_obj = datetime.now()
            date_str = date_obj.strftime("%Y-%m-%d")

        weekday = date_obj.strftime("%A")

        # Use location from data or parameter
        location_name = data.get("location", location)
        if not location_name or location_name == "Unknown Location":
            location_name = location

        # Extract arrival and departure times
        arrival_time = data.get("arrival_time", "08:00")
        departure_time = data.get("departure_time", "12:00")
        
        # Validate time formats
        time_pattern = re.compile(r'^\d{1,2}:\d{2}$')
        if not time_pattern.match(arrival_time):
            arrival_time = "08:00"
        if not time_pattern.match(departure_time):
            departure_time = "12:00"

        # Extract groups from data with validation
        groups = data.get("groups", [])
        valid_groups = []
        
        # Validate each group
        for group in groups:
            if isinstance(group, dict):
                valid_group = {
                    "groupName": group.get("groupName", ""),
                    "color": group.get("color", ""),
                    "departureTime": group.get("departureTime", arrival_time),
                    "returnTime": group.get("returnTime", departure_time),
                    "activity": group.get("activity", "Guided Landing")
                }
                
                # Ensure required fields
                if not valid_group["groupName"] and valid_group["color"]:
                    valid_group["groupName"] = f"{valid_group['color']} Group"
                elif not valid_group["groupName"] and not valid_group["color"]:
                    continue  # Skip invalid groups
                    
                # Ensure color if only group name is present
                if not valid_group["color"] and valid_group["groupName"]:
                    # Extract color from group name if possible
                    color_match = re.search(r'(Blue|Red|Yellow|Green)', valid_group["groupName"], re.IGNORECASE)
                    if color_match:
                        valid_group["color"] = color_match.group(1)
                    else:
                        valid_group["color"] = "Blue"  # Default color
                        
                # Validate time formats
                if not time_pattern.match(valid_group["departureTime"]):
                    valid_group["departureTime"] = arrival_time
                if not time_pattern.match(valid_group["returnTime"]):
                    valid_group["returnTime"] = departure_time
                    
                valid_groups.append(valid_group)
        
        # Create default groups if none found or all were invalid
        if not valid_groups:
            # Create groups based on operation type
            if operation_type == "am_only":
                valid_groups = [
                    {
                        "groupName": "Blue Group",
                        "color": "Blue",
                        "departureTime": arrival_time,
                        "returnTime": departure_time,
                        "activity": "Guided Landing"
                    },
                    {
                        "groupName": "Red Group",
                        "color": "Red",
                        "departureTime": self._add_minutes_to_time(arrival_time, 30),
                        "returnTime": self._add_minutes_to_time(departure_time, 30),
                        "activity": "Guided Landing"
                    }
                ]
            elif operation_type == "pm_only":
                valid_groups = [
                    {
                        "groupName": "Yellow Group",
                        "color": "Yellow",
                        "departureTime": arrival_time,
                        "returnTime": departure_time,
                        "activity": "Zodiac Cruise"
                    },
                    {
                        "groupName": "Green Group",
                        "color": "Green",
                        "departureTime": self._add_minutes_to_time(arrival_time, 30),
                        "returnTime": self._add_minutes_to_time(departure_time, 30),
                        "activity": "Zodiac Cruise"
                    }
                ]
            else:  # combined
                valid_groups = [
                    {
                        "groupName": "Blue Group",
                        "color": "Blue",
                        "departureTime": arrival_time,
                        "returnTime": self._add_minutes_to_time(arrival_time, 120),
                        "activity": "Guided Landing"
                    },
                    {
                        "groupName": "Red Group",
                        "color": "Red",
                        "departureTime": self._add_minutes_to_time(arrival_time, 30),
                        "returnTime": self._add_minutes_to_time(arrival_time, 150),
                        "activity": "Guided Landing"
                    },
                    {
                        "groupName": "Yellow Group",
                        "color": "Yellow",
                        "departureTime": self._add_minutes_to_time(arrival_time, 180),
                        "returnTime": self._add_minutes_to_time(arrival_time, 300),
                        "activity": "Zodiac Cruise"
                    },
                    {
                        "groupName": "Green Group",
                        "color": "Green",
                        "departureTime": self._add_minutes_to_time(arrival_time, 210),
                        "returnTime": self._add_minutes_to_time(arrival_time, 330),
                        "activity": "Zodiac Cruise"
                    }
                ]

        # Extract schedule from data with validation
        schedule = data.get("schedule", [])
        valid_schedule = []
        
        # Validate each schedule item
        for item in schedule:
            if isinstance(item, dict) and "time" in item:
                valid_item = {
                    "time": item.get("time", ""),
                    "type": item.get("type", "activity"),
                    "description": item.get("description", ""),
                    "location": item.get("location", location_name)
                }
                
                # Validate time format
                if not time_pattern.match(valid_item["time"]):
                    continue  # Skip invalid times
                    
                # Ensure description
                if not valid_item["description"]:
                    if valid_item["type"] == "arrival":
                        valid_item["description"] = f"Arrival at {location_name}"
                    elif valid_item["type"] == "departure":
                        valid_item["description"] = f"Departure from {location_name}"
                    else:
                        valid_item["description"] = f"Activity at {valid_item['time']}"
                        
                valid_schedule.append(valid_item)
        
        # Create basic schedule if none found or all were invalid
        if not valid_schedule:
            valid_schedule = [
                {
                    "time": arrival_time,
                    "type": "arrival",
                    "description": f"Arrival at {location_name}",
                    "location": location_name
                }
            ]
            
            # Add activity times based on groups
            activity_times = set()
            for group in valid_groups:
                activity_times.add(group.get("departureTime"))
                activity_times.add(group.get("returnTime"))
            
            # Sort times
            sorted_times = sorted(list(activity_times))
            
            # Add activities (excluding arrival time which is already added)
            for time in sorted_times:
                if time != arrival_time and time != departure_time:
                    valid_schedule.append({
                        "time": time,
                        "type": "activity",
                        "description": f"Group activity at {time}",
                        "location": location_name
                    })
                    
            # Add departure
            valid_schedule.append({
                "time": departure_time,
                "type": "departure",
                "description": f"Departure from {location_name}",
                "location": location_name
            })

        # Extract tides from data with validation
        tides = data.get("tides", [])
        valid_tides = []
        
        # Validate each tide
        for tide in tides:
            if isinstance(tide, dict) and "time" in tide:
                valid_tide = {
                    "time": tide.get("time", ""),
                    "height": tide.get("height", 0.0),
                    "label": tide.get("label", "")
                }
                
                # Validate time format
                if not time_pattern.match(valid_tide["time"]):
                    continue  # Skip invalid times
                    
                # Ensure label
                if not valid_tide["label"]:
                    if isinstance(valid_tide["height"], (int, float)) and valid_tide["height"] > 6.0:
                        valid_tide["label"] = "High Tide"
                    else:
                        valid_tide["label"] = "Low Tide"
                        
                # Ensure height is a number
                if not isinstance(valid_tide["height"], (int, float)):
                    try:
                        valid_tide["height"] = float(valid_tide["height"])
                    except:
                        valid_tide["height"] = 2.0 if "low" in valid_tide["label"].lower() else 12.0
                        
                valid_tides.append(valid_tide)
        
        # Create default tides if none found or all were invalid
        if not valid_tides:
            # Create tides based on operation type
            if operation_type == "am_only":
                valid_tides = [
                    {"time": self._subtract_hours_from_time(arrival_time, 2), "height": 2.0, "label": "Low Tide"},
                    {"time": self._add_hours_to_time(departure_time, 1), "height": 12.0, "label": "High Tide"}
                ]
            elif operation_type == "pm_only":
                valid_tides = [
                    {"time": self._subtract_hours_from_time(arrival_time, 1), "height": 12.0, "label": "High Tide"},
                    {"time": self._add_hours_to_time(departure_time, 2), "height": 2.0, "label": "Low Tide"}
                ]
            else:  # combined
                valid_tides = [
                    {"time": self._subtract_hours_from_time(arrival_time, 1), "height": 2.0, "label": "Low Tide"},
                    {"time": self._add_hours_to_time(arrival_time, 6), "height": 12.0, "label": "High Tide"}
                ]

        # Extract equipment with validation
        equipment = data.get("equipment", {})
        if not isinstance(equipment, dict):
            equipment = {}
            
        zodiacs = equipment.get("zodiacs", 0)
        twins = equipment.get("twins", 0)
        
        # Ensure numeric values
        try:
            zodiacs = int(zodiacs)
        except:
            zodiacs = 8  # Default
            
        try:
            twins = int(twins)
        except:
            twins = 1  # Default
            
        # Ensure reasonable values
        if zodiacs <= 0:
            zodiacs = len(valid_groups) * 2  # 2 zodiacs per group
        if twins <= 0:
            twins = 1
            
        # Extract personnel with validation
        personnel = data.get("personnel", {})
        if not isinstance(personnel, dict):
            personnel = {"total_count": 0, "guides": [], "drivers": []}
            
        # Create zodiac drivers based on zodiac count
        zodiac_drivers = []
        for i in range(1, min(zodiacs + 1, 6)):  # Limit to 5 zodiacs for template
            zodiac_drivers.append({
                "zodiacNumber": i,
                "wave1Drivers": [f"driver-{i}"],
                "wave2Drivers": [f"driver-{i+5}"]
            })
            
        # Create landing guides based on group count
        landing_guides = {
            "wave1Guides": [f"guide-{i+1}" for i in range(min(len(valid_groups), 3))],
            "wave2Guides": [f"guide-{i+4}" for i in range(min(len(valid_groups), 3))]
        }
        
        # Calculate sun times based on date and operation type
        sun_times = self._calculate_sun_times(date_obj, operation_type)
        
        # Extract notes with validation
        notes = data.get("notes", "")
        if not notes:
            if "weather" in data and data["weather"] and data["weather"] != "Not specified":
                notes = f"Weather conditions: {data['weather']}. Landing operations weather dependent."
            else:
                notes = "Landing operations weather dependent."
                
        # Build the expected format with validated data
        template = {
            "dayNumber": 1,  # Default to day 1
            "weekday": weekday,
            "date": date_str,
            "location": location_name,
            "utcOffset": "+08:00",  # Default timezone
            "notes": notes,
            "zodiacs": zodiacs,
            "twins": twins,
            "activityType": self._determine_activity_type(valid_groups, operation_type),
            "groups": valid_groups,
            "schedule": valid_schedule,
            "tides": valid_tides,
            "groupOrder": [group.get("color") for group in valid_groups if group.get("color")] or ["Blue", "Red", "Yellow", "Green"],
            "zodiacDrivers": zodiac_drivers,
            "landingGuides": landing_guides,
            "nextDayLocation": "Next Location",
            "sunTimes": sun_times,
            "operationType": operation_type  # Add operation type explicitly
        }
        
        # Add weather if available
        if "weather" in data and data["weather"] and data["weather"] != "Not specified":
            template["weather"] = data["weather"]

        return template

    def _determine_activity_type(self, groups: List[Dict], operation_type: str) -> str:
        """Determine activity type from groups and operation type."""
        if not groups:
            return "2h Guided Landing (Zodiac Shuttle)"

        # Look at the activity in groups
        activities = [group.get("activity", "") for group in groups]
        if any("landing" in activity.lower() for activity in activities):
            return "2h Guided Landing (Zodiac Shuttle)"
        elif any("cruise" in activity.lower() for activity in activities):
            return "Zodiac Cruise"
        else:
            return "2h Guided Landing (Zodiac Shuttle)"
            
    def _add_minutes_to_time(self, time_str: str, minutes: int) -> str:
        """Add minutes to a time string in HH:MM format."""
        try:
            from datetime import datetime, timedelta
            
            # Parse time
            hours, mins = map(int, time_str.split(':'))
            
            # Create datetime object for today with this time
            dt = datetime.now().replace(hour=hours, minute=mins, second=0, microsecond=0)
            
            # Add minutes
            new_dt = dt + timedelta(minutes=minutes)
            
            # Return in HH:MM format
            return new_dt.strftime("%H:%M")
        except:
            return time_str
            
    def _add_hours_to_time(self, time_str: str, hours: int) -> str:
        """Add hours to a time string in HH:MM format."""
        return self._add_minutes_to_time(time_str, hours * 60)
        
    def _subtract_hours_from_time(self, time_str: str, hours: int) -> str:
        """Subtract hours from a time string in HH:MM format."""
        return self._add_minutes_to_time(time_str, -hours * 60)
        
    def _calculate_sun_times(self, date_obj, operation_type: str) -> dict:
        """Calculate approximate sun times based on date and operation type."""
        # This is a simplified calculation - in a real app you'd use a proper
        # astronomical library or API to get accurate sun times for the location
        
        # Determine month for seasonal adjustments
        month = date_obj.month
        
        # Summer months (Northern Hemisphere)
        if 5 <= month <= 8:
            return {
                "twilightStart": "04:30",
                "sunrise": "05:15",
                "sunset": "19:45",
                "twilightEnd": "20:30"
            }
        # Spring/Fall
        elif month in [3, 4, 9, 10]:
            return {
                "twilightStart": "05:30",
                "sunrise": "06:15",
                "sunset": "18:15",
                "twilightEnd": "19:00"
            }
        # Winter months
        else:
            return {
                "twilightStart": "06:30",
                "sunrise": "07:15",
                "sunset": "17:15",
                "twilightEnd": "18:00"
            }

    def _create_day_plan(
        self, data: Dict[str, Any], operation_type: str, location: str
    ) -> Dict[str, Any]:
        """Create a single day plan from extracted data."""
        # Extract date
        expedition_date = self._extract_date(data)

        # Base day structure
        day_plan = {
            "voyageDay": data.get("day_number", 1),
            "date": expedition_date,
            "location": location,
            "eta": self._extract_arrival_time(data, operation_type),
            "etd": self._extract_departure_time(data, operation_type),
            "remarks": data.get("notes", ""),
            "tides": self._extract_tides(data),
            "activityPlan": self._create_activity_plan(
                data, operation_type, location, expedition_date
            ),
        }

        return day_plan

    def _create_activity_plan(
        self,
        data: Dict[str, Any],
        operation_type: str,
        location: str,
        expedition_date: str,
    ) -> Dict[str, Any]:
        """Create activity plan based on operation type."""
        now = datetime.now()

        activity_plan = {
            "voyageDay": data.get("day_number", 1),
            "date": expedition_date,
            "location": location,
            "hasZodiacCruise": "zodiac" in data.get("activities", []),
            "hasLanding": "landing" in data.get("activities", []),
            "briefings": self._extract_briefings(data),
            "lectures": self._extract_lectures(data),
            "specialEvents": self._extract_special_events(data),
            "recaps": self._extract_recaps(data),
            "notes": self._format_notes_with_equipment(data),
            "groupOrder": ["Blue", "Red", "Yellow", "Green"],
            "createdAt": now.isoformat(),
            "lastModified": now.isoformat(),
        }

        # Add operation-specific details
        if activity_plan["hasZodiacCruise"]:
            activity_plan["zodiacCruiseOperation"] = self._create_zodiac_operation(
                data, operation_type
            )

        if activity_plan["hasLanding"]:
            activity_plan["landingOperation"] = self._create_landing_operation(
                data, operation_type
            )

        return activity_plan

    def _create_zodiac_operation(
        self, data: Dict[str, Any], operation_type: str
    ) -> Dict[str, Any]:
        """Create zodiac cruise operation details."""
        return {
            "zodiacCount": data.get("zodiac_count", 8),
            "twinsCount": data.get("twins_count", 1),
            "dropTime": self._get_operation_start_time(data, operation_type),
            "cruiseDuration": data.get("duration", "2h"),
            "estimatedDropDuration": 15,
            "guestGroups": 4,
            "groupStartTimes": self._calculate_group_times(data, operation_type),
            "drivers": self._extract_drivers(data),
        }

    def _create_landing_operation(
        self, data: Dict[str, Any], operation_type: str
    ) -> Dict[str, Any]:
        """Create landing operation details."""
        return {
            "zodiacCount": data.get("zodiac_count", 8),
            "twinsCount": data.get("twins_count", 1),
            "dropTime": self._get_operation_start_time(data, operation_type),
            "estimatedDropDuration": 40,
            "landingType": data.get("landing_type", "Zodiac Shuttle"),
            "activityMode": data.get("activity_mode", "Guided"),
            "activityDuration": data.get("duration", "2h"),
            "assignedGuides": self._extract_guides(data),
            "beachSetupNotes": data.get("setup_notes", ""),
            "groupSchedule": self._create_group_schedule(data, operation_type),
            "drivers": self._extract_drivers(data),
        }

    def _extract_date(self, data: Dict[str, Any]) -> str:
        """Extract and format date from data."""
        date_str = data.get("date")
        if date_str:
            # Try to parse and reformat to ensure consistency
            try:
                if isinstance(date_str, str):
                    # Handle various date formats
                    for fmt in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y", "%Y/%m/%d"]:
                        try:
                            parsed_date = datetime.strptime(date_str, fmt)
                            return parsed_date.strftime("%Y-%m-%d")
                        except ValueError:
                            continue
                return date_str  # Return as-is if parsing fails
            except Exception:
                pass

        # Default to today if no date found
        return datetime.now().strftime("%Y-%m-%d")

    def _extract_tides(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract tide information from data."""
        tides = data.get("tides", [])
        formatted_tides = []

        for tide in tides:
            if isinstance(tide, dict):
                formatted_tide = {
                    "time": tide.get("time", "00:00"),
                    "height": float(tide.get("height", 0.0)),
                    "type": tide.get("type", "unknown"),
                }
                formatted_tides.append(formatted_tide)

        return formatted_tides

    def _format_notes_with_equipment(self, data: Dict[str, Any]) -> str:
        """Format notes with equipment information."""
        notes = data.get("notes", "")
        equipment = data.get("equipment", [])

        if equipment:
            equipment_section = "\n\nEquipment:\n"
            for item in equipment:
                equipment_section += f"• {item}\n"
            notes += equipment_section

        setup_notes = data.get("setup_notes", "")
        if setup_notes:
            notes += f"\n\nSetup Notes:\n• {setup_notes}"

        return notes.strip()

    def _get_operation_start_time(
        self, data: Dict[str, Any], operation_type: str
    ) -> str:
        """Get operation start time based on type."""
        if operation_type == "am_only":
            return data.get("start_time", "08:00")
        elif operation_type == "pm_only":
            return data.get("start_time", "14:00")
        else:  # combined
            return data.get("start_time", "08:00")

    def _calculate_group_times(
        self, data: Dict[str, Any], operation_type: str
    ) -> Dict[str, str]:
        """Calculate group start times."""
        start_time = self._get_operation_start_time(data, operation_type)

        # Simple time calculation (would be more sophisticated in real implementation)
        return {
            "Blue": start_time,
            "Red": start_time,  # Would add intervals in real implementation
            "Yellow": start_time,
            "Green": start_time,
        }

    def _create_group_schedule(
        self, data: Dict[str, Any], operation_type: str
    ) -> List[Dict[str, Any]]:
        """Create group schedule for landing operations."""
        # Simplified group schedule
        return [
            {
                "groupName": "Blue",
                "groupColor": "#3B82F6",
                "startTime": self._get_operation_start_time(data, operation_type),
                "returnTime": "10:00",  # Would calculate based on duration
                "wave": 1,
                "isEditable": False,
            }
        ]

    def _extract_briefings(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract briefing information."""
        return data.get("briefings", [])

    def _extract_lectures(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract lecture information."""
        return data.get("lectures", [])

    def _extract_special_events(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract special events."""
        return data.get("special_events", [])

    def _extract_recaps(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract recap information."""
        return data.get("recaps", [])

    def _extract_drivers(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract zodiac driver information."""
        return data.get("drivers", [])

    def _extract_guides(self, data: Dict[str, Any]) -> List[str]:
        """Extract guide assignments."""
        return data.get("guides", [])

    def _extract_arrival_time(self, data: Dict[str, Any], operation_type: str) -> str:
        """Extract arrival time based on operation type."""
        if operation_type == "pm_only":
            return data.get("arrival_time", "12:00")
        return data.get("arrival_time", "07:00")

    def _extract_departure_time(self, data: Dict[str, Any], operation_type: str) -> str:
        """Extract departure time based on operation type."""
        if operation_type == "am_only":
            return data.get("departure_time", "12:00")
        return data.get("departure_time", "18:00")

    def _create_global_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create global voyage information."""
        return {
            "totalGuests": data.get("total_guests", 120),
            "utcOffset": data.get("utc_offset", "+00:00"),
            "colorGroups": [
                {"name": "Blue", "color": "#3B82F6", "enabled": True, "passengers": 30},
                {"name": "Red", "color": "#EF4444", "enabled": True, "passengers": 30},
                {
                    "name": "Yellow",
                    "color": "#F59E0B",
                    "enabled": True,
                    "passengers": 30,
                },
                {
                    "name": "Green",
                    "color": "#10B981",
                    "enabled": True,
                    "passengers": 30,
                },
            ],
            "expeditionTeam": {
                "totalMembers": len(data.get("team_members", [])),
                "members": data.get("team_members", []),
            },
        }

    def _extract_team_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract expedition team data."""
        return data.get("team_members", [])

    def _validate_template(self, template: Dict[str, Any]) -> str:
        """Validate generated template against the expected simple format."""
        try:
            # Check for simple format (what we want)
            simple_required_fields = ["dayNumber", "weekday", "date", "location", "groups", "schedule"]

            # Check if this is the simple format
            has_simple_fields = all(field in template for field in simple_required_fields[:3])

            if has_simple_fields:
                # Validate simple format
                for field in simple_required_fields:
                    if field not in template:
                        return f"Missing required field: {field}"

                # Validate groups array
                if not isinstance(template.get("groups"), list):
                    return "Groups must be a list"

                # Validate schedule array
                if not isinstance(template.get("schedule"), list):
                    return "Schedule must be a list"

                return "valid"

            else:
                # Legacy complex format validation
                required_fields = ["exportedAt", "days", "globalVoyageInfo"]
                for field in required_fields:
                    if field not in template:
                        return f"Missing required field: {field}"

                # Validate days array
                if not template["days"]:
                    return "Days array cannot be empty"

                # Validate each day
                for day in template["days"]:
                    day_validation = self._validate_day_plan(day)
                    if day_validation != "valid":
                        return f"Day validation failed: {day_validation}"

                return "valid"

        except Exception as e:
            return f"Validation error: {e!s}"

    def _validate_day_plan(self, day: Dict[str, Any]) -> str:
        """Validate a single day plan."""
        required_fields = ["date", "location"]
        for field in required_fields:
            if field not in day or not day[field]:
                return f"Missing required day field: {field}"

        # Validate date format
        date_pattern = self._validation_rules["date_format"]
        import re

        if not re.match(date_pattern, day["date"]):
            return f"Invalid date format: {day['date']}"

        return "valid"

    def _save_template(
        self,
        template: Dict[str, Any],
        output_path: str,
        operation_type: str,
        location: str,
    ) -> str:
        """Save template to file."""
        # Create output directory if it doesn't exist
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Ensure location has underscores instead of spaces and clean other special characters
        location_clean = location.replace(" ", "_").replace("/", "_").replace("-", "_")
        # Make sure operation_type also uses underscores
        operation_type_clean = operation_type.replace(" ", "_").replace("-", "_")
        filename = f"{location_clean}_{operation_type_clean}_{timestamp}.json"

        file_path = output_dir / filename

        # Save JSON file
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(template, f, indent=2, ensure_ascii=False, default=str)

        return str(file_path)

    async def _arun(
        self, extracted_data: str, operation_type: str, location: str, output_path: str
    ) -> str:
        """Async version of template generation."""
        return self._run(extracted_data, operation_type, location, output_path)


def create_template_generator_tool():
    """Create a template generator tool for use in LangChain agents."""
    return TemplateGeneratorTool()
