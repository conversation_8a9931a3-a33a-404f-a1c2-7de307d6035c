/**
 * JSON Preview functionality for the Expedition Planner
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const jsonPreviewElement = document.getElementById('json-preview');
    const toggleJsonViewBtn = document.getElementById('toggle-json-view');
    const copyJsonBtn = document.getElementById('copy-json');
    const viewJsonBtn = document.getElementById('view-json');
    
    // State
    let currentJsonData = null;
    let isFormatted = true;
    
    // Event listeners
    if (toggleJsonViewBtn) {
        toggleJsonViewBtn.addEventListener('click', toggleJsonFormat);
    }
    
    if (copyJsonBtn) {
        copyJsonBtn.addEventListener('click', copyJsonToClipboard);
    }
    
    if (viewJsonBtn) {
        viewJsonBtn.addEventListener('click', viewFirstJsonFile);
    }
    
    // Socket.io event listener for processing complete
    if (typeof io !== 'undefined') {
        const socket = io();
        
        socket.on('processing_complete', function(data) {
            if (data.json_files) {
                // Store the JSON files data
                currentJsonData = data.json_files;
                
                // Automatically view the first JSON file
                viewFirstJsonFile();
            }
        });
    }
    
    /**
     * Toggle between formatted and compact JSON display
     */
    function toggleJsonFormat() {
        if (!currentJsonData) return;
        
        isFormatted = !isFormatted;
        updateJsonPreview(jsonPreviewElement.textContent);
    }
    
    /**
     * Copy JSON content to clipboard
     */
    function copyJsonToClipboard() {
        if (!jsonPreviewElement.textContent) return;
        
        navigator.clipboard.writeText(jsonPreviewElement.textContent)
            .then(() => {
                // Show success message
                const originalText = copyJsonBtn.innerHTML;
                copyJsonBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                
                setTimeout(() => {
                    copyJsonBtn.innerHTML = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('Failed to copy: ', err);
            });
    }
    
    /**
     * View the first available JSON file
     */
    function viewFirstJsonFile() {
        if (!currentJsonData) return;
        
        // Get the first location
        const locations = Object.keys(currentJsonData);
        if (locations.length === 0) return;
        
        const firstLocation = locations[0];
        const locationFiles = currentJsonData[firstLocation];
        
        // Get the first operation type
        const operationTypes = Object.keys(locationFiles);
        if (operationTypes.length === 0) return;
        
        const firstOperationType = operationTypes[0];
        const filePath = locationFiles[firstOperationType];
        
        // Fetch and display the JSON file
        fetchJsonFile(filePath);
    }
    
    /**
     * Fetch a JSON file and display it in the preview
     */
    function fetchJsonFile(filePath) {
        fetch(`/api/download/${encodeURIComponent(filePath)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                updateJsonPreview(data);
            })
            .catch(error => {
                console.error('Error fetching JSON file:', error);
                jsonPreviewElement.textContent = `Error loading JSON: ${error.message}`;
            });
    }
    
    /**
     * Update the JSON preview with the provided data
     */
    function updateJsonPreview(data) {
        if (!data) return;
        
        let jsonString;
        
        // Convert to string if it's an object
        if (typeof data === 'object') {
            jsonString = JSON.stringify(data, null, isFormatted ? 2 : 0);
        } else {
            // If it's already a string, try to parse and re-stringify for formatting
            try {
                const jsonObj = JSON.parse(data);
                jsonString = JSON.stringify(jsonObj, null, isFormatted ? 2 : 0);
            } catch (e) {
                // If parsing fails, use the original string
                jsonString = data;
            }
        }
        
        // Update the preview
        jsonPreviewElement.textContent = jsonString;
        
        // Apply syntax highlighting if available
        if (window.hljs) {
            hljs.highlightElement(jsonPreviewElement);
        }
    }
});