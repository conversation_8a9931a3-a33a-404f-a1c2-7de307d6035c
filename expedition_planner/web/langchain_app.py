"""
Flask web application for LangChain-based expedition document processing.
"""

import json
import logging
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from flask import (
    Flask,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    send_file,
    url_for,
)
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from werkzeug.utils import secure_filename

from ..config import get_config
from ..config.langchain_config import get_langchain_config
from ..core.langchain_processor import LangChainExpeditionProcessor

logger = logging.getLogger(__name__)

# Global variables
active_sessions = {}
langchain_processor = LangChainExpeditionProcessor()
socketio = None


def clean_result_for_json(result):
    """Clean result object to make it JSON serializable."""
    if isinstance(result, dict):
        cleaned = {}
        for key, value in result.items():
            if key in ["intermediate_steps", "agent_scratchpad"]:
                # Skip non-serializable agent data
                continue
            elif isinstance(value, (dict, list)):
                cleaned[key] = clean_result_for_json(value)
            elif hasattr(value, "__dict__"):
                # Convert objects to string representation
                cleaned[key] = str(value)
            else:
                cleaned[key] = value
        return cleaned
    elif isinstance(result, list):
        return [clean_result_for_json(item) for item in result]
    elif hasattr(result, "__dict__"):
        return str(result)
    else:
        return result


def create_langchain_app(config_override: Optional[Dict[str, Any]] = None) -> Flask:  # noqa: C901
    """Create and configure the Flask application with LangChain support."""
    app = Flask(__name__, static_folder='static')
    global socketio

    # Load configuration
    config = get_config()

    if config_override:
        config.update(config_override)

    app.config.update(config)
    app.secret_key = config.get("secret_key", "dev-key-change-in-production")

    # Initialize SocketIO for real-time agent updates
    socketio = SocketIO(app, cors_allowed_origins="*")

    # Ensure directories exist
    upload_dir = Path(config["directories"]["uploads"])
    output_dir = Path(config["directories"]["outputs"])
    upload_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)

    # Routes
    @app.route("/")
    def index():
        """Main page with expedition report converter."""
        return render_template("langchain_index.html")

    @app.route("/agent-interface")
    def agent_interface():
        """Document conversion page."""
        return render_template("agent_interface.html")

    @app.route("/pattern-analysis")
    def pattern_analysis():
        """Pattern analysis page."""
        return render_template("pattern_analysis.html")

    @app.route("/api/upload-documents", methods=["POST"])
    def upload_documents():
        """Handle document upload for LangChain processing."""
        try:
            if "files" not in request.files:
                return jsonify({"success": False, "error": "No files provided"})

            files = request.files.getlist("files")
            if not files or all(f.filename == "" for f in files):
                return jsonify({"success": False, "error": "No files selected"})

            # Create session
            session_id = str(uuid.uuid4())
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_dir = upload_dir / f"{session_id}_{timestamp}"
            session_dir.mkdir(exist_ok=True)

            uploaded_files = []

            for file in files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    file_path = session_dir / filename
                    file.save(str(file_path))

                    uploaded_files.append(
                        {
                            "name": filename,
                            "path": str(file_path),
                            "size": file_path.stat().st_size,
                        }
                    )

            # Store session info
            active_sessions[session_id] = {
                "session_id": session_id,
                "upload_time": datetime.now(),
                "files": uploaded_files,
                "session_dir": str(session_dir),
                "status": "uploaded",
            }

            logger.info(
                f"Uploaded {len(uploaded_files)} files for session {session_id}"
            )

            return jsonify(
                {
                    "success": True,
                    "session_id": session_id,
                    "files": uploaded_files,
                    "message": f"Successfully uploaded {len(uploaded_files)} files",
                }
            )

        except Exception as e:
            logger.error(f"Error uploading documents: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/process-expedition", methods=["POST"])
    def process_expedition():
        """Start LangChain-based expedition processing with focus on JSON generation."""
        try:
            data = request.get_json()
            session_id = data.get("session_id")
            expedition_name = data.get("expedition_name", "Unnamed Expedition")
            # Default to False - focus on JSON generation first
            enable_analysis = data.get("enable_analysis", False)

            if session_id not in active_sessions:
                return jsonify({"success": False, "error": "Invalid session ID"})

            session_info = active_sessions[session_id]
            session_dir = session_info["session_dir"]

            # Create output directory
            output_dir_path = output_dir / f"expedition_{session_id}"
            output_dir_path.mkdir(exist_ok=True)

            # Start processing in background
            def process_in_background():
                try:
                    # Emit status updates via WebSocket
                    socketio.emit(
                        "agent_status",
                        {"agent": "json_generator", "status": "processing", "message": "Extracting data and generating JSON templates"},
                        room=session_id,
                    )

                    # Process expedition with focus on JSON generation
                    result = langchain_processor.process_expedition(
                        documents_directory=session_dir,
                        expedition_name=expedition_name,
                        output_directory=str(output_dir_path),
                        enable_analysis=enable_analysis,
                    )

                    # Clean result for JSON serialization
                    cleaned_result = clean_result_for_json(result)

                    # Update session with results
                    active_sessions[session_id]["result"] = cleaned_result
                    active_sessions[session_id]["status"] = (
                        "completed" if result.get("success") else "failed"
                    )

                    # Extract JSON files from the result
                    json_files = {}
                    
                    # Check for JSON files in the workflow state
                    if "steps" in result and "json_generation" in result["steps"]:
                        json_gen_results = result["steps"]["json_generation"]
                        
                        # Process each location's JSON generation results
                        for location, gen_result in json_gen_results.items():
                            if gen_result.get("success") and "generated_files" in gen_result:
                                json_files[location] = gen_result["generated_files"]
                    
                    # Emit completion with phase information and JSON files
                    completion_message = {
                        **cleaned_result,
                        "phase": "json_generation",
                        "next_step": "pattern_analysis" if not enable_analysis else "complete",
                        "json_files": json_files  # Add JSON files to the response
                    }

                    # Log the JSON files being sent to the client
                    logger.info(f"Sending JSON files to client: {json_files}")
                    
                    socketio.emit(
                        "processing_complete", completion_message, room=session_id
                    )

                except Exception as e:
                    logger.error(f"Background processing error: {e}")
                    socketio.emit("error", {"message": str(e)}, room=session_id)

            # Start background processing
            socketio.start_background_task(process_in_background)

            processing_type = "JSON Generation" if not enable_analysis else "Full Processing (JSON + Analysis)"

            return jsonify(
                {
                    "success": True,
                    "session_id": session_id,
                    "message": f"{processing_type} started",
                    "processing_type": "json_generation" if not enable_analysis else "full_processing"
                }
            )

        except Exception as e:
            logger.error(f"Error starting expedition processing: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/session-status/<session_id>")
    def get_session_status(session_id):
        """Get status of a processing session."""
        try:
            if session_id in active_sessions:
                session = active_sessions[session_id]
                return jsonify({"success": True, "session": session})
            else:
                return jsonify({"success": False, "error": "Session not found"})

        except Exception as e:
            logger.error(f"Error getting session status: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/analyze-json-files", methods=["POST"])
    def analyze_json_files():
        """Analyze existing JSON files for patterns using the enhanced batch analysis method."""
        try:
            data = request.get_json()
            json_file_paths = data.get("json_files", [])

            if not json_file_paths:
                return jsonify({"success": False, "error": "No JSON files provided"})

            if len(json_file_paths) < 2:
                return jsonify({
                    "success": False,
                    "error": "Pattern analysis requires at least 2 JSON files for meaningful comparison",
                    "recommendation": "Select multiple expedition operation files to discover patterns"
                })

            # Use the enhanced batch analysis method
            analysis_result = langchain_processor.analyze_multiple_json_files(json_file_paths)

            return jsonify({
                "success": analysis_result.get("success", False),
                **clean_result_for_json(analysis_result)
            })

        except Exception as e:
            logger.error(f"Error analyzing JSON files: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/list-json-files")
    def list_json_files():
        """List available JSON files for analysis."""
        try:
            json_files = []

            # Scan output directories for JSON files
            if output_dir.exists():
                for expedition_dir in output_dir.iterdir():
                    if expedition_dir.is_dir():
                        for json_file in expedition_dir.glob("*.json"):
                            # Skip workflow state files
                            if "workflow_state" not in json_file.name:
                                file_info = {
                                    "path": str(json_file),
                                    "name": json_file.name,
                                    "expedition": expedition_dir.name,
                                    "size": json_file.stat().st_size,
                                    "modified": datetime.fromtimestamp(
                                        json_file.stat().st_mtime
                                    ).isoformat(),
                                }
                                json_files.append(file_info)

            # Sort by modification time (newest first)
            json_files.sort(key=lambda x: x["modified"], reverse=True)

            return jsonify(
                {
                    "success": True,
                    "json_files": json_files,
                    "total_files": len(json_files),
                }
            )

        except Exception as e:
            logger.error(f"Error listing JSON files: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/processor-capabilities")
    def get_processor_capabilities():
        """Get LangChain processor capabilities."""
        try:
            capabilities = langchain_processor.get_processor_capabilities()
            return jsonify({"success": True, "capabilities": capabilities})

        except Exception as e:
            logger.error(f"Error getting capabilities: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/validate-configuration")
    def validate_configuration():
        """Validate LangChain processor configuration."""
        try:
            validation = langchain_processor.validate_configuration()
            return jsonify({"success": True, "validation": validation})

        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/download/<path:filename>")
    def download_file(filename):
        """Download generated files."""
        try:
            file_path = Path(filename)
            if file_path.exists() and file_path.is_file():
                # Check if the request wants JSON content or file download
                if request.args.get('format') == 'json' or request.headers.get('Accept') == 'application/json':
                    # Return JSON content
                    with open(file_path, 'r') as f:
                        content = f.read()
                    return jsonify(json.loads(content))
                else:
                    # Return file for download
                    return send_file(str(file_path), as_attachment=True)
            else:
                return jsonify({"error": "File not found"}), 404

        except Exception as e:
            logger.error(f"Error downloading file: {e}")
            return jsonify({"error": str(e)}), 500
            
    @app.route("/api/view-json/<path:filename>")
    def view_json_file(filename):
        """View JSON file content."""
        try:
            file_path = Path(filename)
            if file_path.exists() and file_path.is_file():
                with open(file_path, 'r') as f:
                    content = f.read()
                return jsonify(json.loads(content))
            else:
                return jsonify({"error": "File not found"}), 404

        except Exception as e:
            logger.error(f"Error viewing JSON file: {e}")
            return jsonify({"error": str(e)}), 500

    # WebSocket events
    @socketio.on("connect")
    def handle_connect():
        """Handle WebSocket connection."""
        logger.info(f"Client connected: {request.sid}")
        emit("connected", {"message": "Connected to LangChain agent interface"})

    @socketio.on("disconnect")
    def handle_disconnect():
        """Handle WebSocket disconnection."""
        logger.info(f"Client disconnected: {request.sid}")

    @socketio.on("join_session")
    def handle_join_session(data):
        """Join a processing session room."""
        session_id = data.get("session_id")
        if session_id:
            join_room(session_id)
            emit("joined_session", {"session_id": session_id})

    @socketio.on("leave_session")
    def handle_leave_session(data):
        """Leave a processing session room."""
        session_id = data.get("session_id")
        if session_id:
            leave_room(session_id)
            emit("left_session", {"session_id": session_id})

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template("error.html", error="Page not found"), 404

    @app.errorhandler(500)
    def internal_error(error):
        return render_template("error.html", error="Internal server error"), 500

    return app


def run_langchain_app(host="0.0.0.0", port=8080, debug=False):
    """Run the LangChain Flask application."""
    app = create_langchain_app()

    logger.info("Starting LangChain expedition processor web interface")
    logger.info(f"Server: http://{host}:{port}")

    socketio.run(app, host=host, port=port, debug=debug)


if __name__ == "__main__":
    run_langchain_app(debug=True)
