#!/usr/bin/env python3
"""
Test script to verify JSON generation works correctly.
"""

import json
import logging
from pathlib import Path

from expedition_planner.tools.template_generator import TemplateGeneratorTool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_json_generation():
    """Test the JSON generation with sample data."""
    
    # Create sample expedition data
    sample_data = {
        "date": "2024-07-12",
        "location": "The Lacepedes",
        "arrival_time": "07:40",
        "departure_time": "09:40",
        "operation_type": "combined",
        "groups": [
            {
                "groupName": "Yellow",
                "color": "Yellow",
                "departureTime": "07:40",
                "returnTime": "09:40",
                "activity": "Zodiac Cruise"
            }
        ],
        "schedule": [
            {
                "time": "07:40",
                "type": "arrival",
                "description": "Scheduled arrival time in The Lacepedes",
                "location": "The Lacepedes"
            },
            {
                "time": "07:40",
                "type": "drop_zodiacs",
                "description": "Drop 8 Zodiac + 1 Twin",
                "zodiacsCount": 8,
                "twinsCount": 1
            }
        ],
        "tides": [
            {
                "time": "02:00",
                "height": 2.0,
                "label": "Low Tide"
            }
        ],
        "equipment": {
            "zodiacs": 8,
            "twins": 1,
            "other": []
        },
        "personnel": {
            "total_count": 10,
            "guides": ["guide-1", "guide-2"],
            "drivers": ["driver-1", "driver-2"]
        },
        "weather": "Clear skies, light winds",
        "notes": "Equipment:\n• Table\n• Tablecloth\n• Water\n\nSetup Notes:\n• Beach setup required\n• Safety briefing mandatory"
    }
    
    # Create template generator
    generator = TemplateGeneratorTool()
    
    # Convert data to JSON string
    data_json = json.dumps(sample_data)
    
    # Generate template
    logger.info("Generating JSON template...")
    result = generator._run(
        extracted_data=data_json,
        operation_type="combined",
        location="The Lacepedes",
        output_path="consolidated_outputs"
    )
    
    logger.info(f"Generation result: {result}")
    
    # Check if consolidated_outputs directory exists and has files
    consolidated_dir = Path("consolidated_outputs")
    if consolidated_dir.exists():
        json_files = list(consolidated_dir.glob("*.json"))
        logger.info(f"Found {len(json_files)} JSON files in consolidated_outputs:")
        for file in json_files:
            logger.info(f"  - {file.name} ({file.stat().st_size} bytes)")
            
            # Show content of the file
            with open(file, 'r') as f:
                content = json.load(f)
                logger.info(f"File content preview: {json.dumps(content, indent=2)[:500]}...")
    else:
        logger.warning("consolidated_outputs directory does not exist")

if __name__ == "__main__":
    test_json_generation()
