{"session_id": "67aba698-dcf3-42d5-b539-56e4779968bb", "expedition_name": "King <PERSON> Test", "start_time": "2025-06-27T15:43:09.515063", "documents_directory": "test_documents", "output_directory": "consolidated_outputs", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 1, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "location": "King <PERSON>", "document_count": 1, "processed_documents": 0, "consolidated_data": {}}}, "pattern_analysis": {"success": true, "skipped": true, "reason": "Pattern analysis skipped - run separately on multiple JSON files for better results", "recommendation": "Generate JSON templates first, then run pattern analysis on multiple operations"}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "consolidated_outputs/Unknown_Location-2025-06-27.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "json_generation", "end_time": "2025-06-27T15:43:09.524623", "summary": {"expedition_name": "King <PERSON> Test", "processing_time": "0:00:00.009560", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "consolidated_outputs"}}