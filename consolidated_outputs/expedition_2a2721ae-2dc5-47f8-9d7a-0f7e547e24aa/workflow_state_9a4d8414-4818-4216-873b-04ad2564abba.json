{"session_id": "9a4d8414-4818-4216-873b-04ad2564abba", "expedition_name": "refds", "start_time": "2025-06-27T15:43:21.270056", "documents_directory": "/Users/<USER>/Desktop/Projects/File/expedition_planner/uploads/2a2721ae-2dc5-47f8-9d7a-0f7e547e24aa_20250627_154320", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_2a2721ae-2dc5-47f8-9d7a-0f7e547e24aa", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 1, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "location": "King <PERSON>", "document_count": 1, "processed_documents": 0, "consolidated_data": {}}}, "pattern_analysis": {"success": true, "limited_data": true, "analysis": "Insufficient data for meaningful pattern analysis. Pattern analysis requires multiple operations to compare.", "recommendation": "Collect more operation data (at least 3-5 operations) for meaningful pattern analysis."}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "consolidated_outputs/Unknown_Location-2025-06-27.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "full_processing", "end_time": "2025-06-27T15:45:09.195887", "summary": {"expedition_name": "refds", "processing_time": "0:01:47.925831", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_2a2721ae-2dc5-47f8-9d7a-0f7e547e24aa"}}