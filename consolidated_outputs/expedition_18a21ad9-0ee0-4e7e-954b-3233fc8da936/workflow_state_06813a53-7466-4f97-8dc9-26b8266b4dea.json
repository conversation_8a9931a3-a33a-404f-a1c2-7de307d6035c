{"session_id": "06813a53-7466-4f97-8dc9-26b8266b4dea", "expedition_name": "eww", "start_time": "2025-06-27T15:14:58.441071", "documents_directory": "/Users/<USER>/Desktop/Projects/File/expedition_planner/uploads/18a21ad9-0ee0-4e7e-954b-3233fc8da936_20250627_151455", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_18a21ad9-0ee0-4e7e-954b-3233fc8da936", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 4, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "method": "enhanced_terminology_handling", "location": "King <PERSON>", "terminology_variants_handled": ["ETA", "arrival time", "ship arrives", "ship arrived", "departure", "ship departs", "depart time", "disembark", "disembarkation", "landing", "return", "pickup", "collection"], "consolidated_data": {"location": "King <PERSON>", "operation_type": "combined", "extracted_from_documents": 4, "terminology_standardized": true}}}, "pattern_analysis": {"success": true, "skipped": true, "reason": "Pattern analysis skipped - run separately on multiple JSON files for better results", "recommendation": "Generate JSON templates first, then run pattern analysis on multiple operations"}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_18a21ad9-0ee0-4e7e-954b-3233fc8da936/King_<PERSON>_River_combined_20250627_151458.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "json_generation", "end_time": "2025-06-27T15:14:58.453644", "summary": {"expedition_name": "eww", "processing_time": "0:00:00.012573", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_18a21ad9-0ee0-4e7e-954b-3233fc8da936"}}