{"session_id": "4a6c1292-76d7-473d-a9b0-da177bf54417", "expedition_name": "trerw", "start_time": "2025-06-27T15:14:04.194495", "documents_directory": "/Users/<USER>/Desktop/Projects/File/expedition_planner/uploads/e7167e66-9436-4c78-9d90-e949bb68082c_20250627_151402", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_e7167e66-9436-4c78-9d90-e949bb68082c", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 4, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "method": "enhanced_terminology_handling", "location": "King <PERSON>", "terminology_variants_handled": ["ETA", "arrival time", "ship arrives", "ship arrived", "departure", "ship departs", "depart time", "disembark", "disembarkation", "landing", "return", "pickup", "collection"], "consolidated_data": {"location": "King <PERSON>", "operation_type": "combined", "extracted_from_documents": 4, "terminology_standardized": true}}}, "pattern_analysis": {"success": true, "skipped": true, "reason": "Pattern analysis skipped - run separately on multiple JSON files for better results", "recommendation": "Generate JSON templates first, then run pattern analysis on multiple operations"}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_e7167e66-9436-4c78-9d90-e949bb68082c/King_<PERSON>_River_combined_20250627_151404.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "json_generation", "end_time": "2025-06-27T15:14:04.225264", "summary": {"expedition_name": "trerw", "processing_time": "0:00:00.030769", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_e7167e66-9436-4c78-9d90-e949bb68082c"}}