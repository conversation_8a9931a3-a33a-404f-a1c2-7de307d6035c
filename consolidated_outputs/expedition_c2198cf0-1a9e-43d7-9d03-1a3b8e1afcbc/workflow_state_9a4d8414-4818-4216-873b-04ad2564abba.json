{"session_id": "9a4d8414-4818-4216-873b-04ad2564abba", "expedition_name": "2w", "start_time": "2025-06-27T15:43:48.217507", "documents_directory": "/Users/<USER>/Desktop/Projects/File/expedition_planner/uploads/c2198cf0-1a9e-43d7-9d03-1a3b8e1afcbc_20250627_154347", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_c2198cf0-1a9e-43d7-9d03-1a3b8e1afcbc", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 1, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "location": "King <PERSON>", "document_count": 1, "processed_documents": 0, "consolidated_data": {}}}, "pattern_analysis": {"success": true, "limited_data": true, "analysis": "Insufficient data for meaningful pattern analysis. Pattern analysis requires multiple operations to compare.", "recommendation": "Collect more operation data (at least 3-5 operations) for meaningful pattern analysis."}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "consolidated_outputs/Unknown_Location-2025-06-27.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "full_processing", "end_time": "2025-06-27T15:45:09.199730", "summary": {"expedition_name": "2w", "processing_time": "0:01:20.982223", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "/Users/<USER>/Desktop/Projects/File/consolidated_outputs/expedition_c2198cf0-1a9e-43d7-9d03-1a3b8e1afcbc"}}