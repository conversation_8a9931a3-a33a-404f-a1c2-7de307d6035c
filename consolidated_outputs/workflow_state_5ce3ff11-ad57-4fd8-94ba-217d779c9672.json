{"session_id": "5ce3ff11-ad57-4fd8-94ba-217d779c9672", "expedition_name": "King <PERSON> Test", "start_time": "2025-06-27T15:43:44.086778", "documents_directory": "test_documents", "output_directory": "consolidated_outputs", "status": "completed", "steps": {"organization": {"success": true, "method": "enhanced_detection", "detected_location": "King <PERSON>", "document_count": 1, "focus": "json_template_generation"}, "extraction": {"King George River": {"success": true, "location": "King <PERSON>", "document_count": 1, "processed_documents": 0, "consolidated_data": {}}}, "pattern_analysis": {"success": true, "skipped": true, "reason": "Pattern analysis skipped - run separately on multiple JSON files for better results", "recommendation": "Generate JSON templates first, then run pattern analysis on multiple operations"}, "json_generation": {"King George River": {"success": true, "generated_files": {"combined": "consolidated_outputs/Unknown_Location-2025-06-27.json"}, "operation_types": ["combined"], "total_files": 1}}}, "processing_focus": "json_generation", "end_time": "2025-06-27T15:44:29.942250", "summary": {"expedition_name": "King <PERSON> Test", "processing_time": "0:00:45.855472", "documents_processed": 0, "location_groups": 0, "successful_extractions": 1, "json_files_generated": 1, "pattern_analysis_completed": true, "status": "completed", "output_directory": "consolidated_outputs"}}